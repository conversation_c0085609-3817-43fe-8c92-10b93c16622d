<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="专业前端设计师作品集，提供现代化网站设计、UI/UX设计和响应式开发服务">
    <meta name="keywords" content="前端设计,网站开发,UI设计,UX设计,响应式设计,作品集">
    <meta name="author" content="Portfolio Designer">
    <title>精美作品集 - 专业前端设计</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="alternate icon" href="favicon.ico">

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" as="style">
    <link rel="preload" href="styles.css" as="style">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="http://localhost:8000/">
    <meta property="og:title" content="精美作品集 - 专业前端设计">
    <meta property="og:description" content="专业前端设计师作品集，提供现代化网站设计、UI/UX设计和响应式开发服务">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="http://localhost:8000/">
    <meta property="twitter:title" content="精美作品集 - 专业前端设计">
    <meta property="twitter:description" content="专业前端设计师作品集，提供现代化网站设计、UI/UX设计和响应式开发服务">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">Portfolio</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">首页</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">关于</a>
                </li>
                <li class="nav-item">
                    <a href="#portfolio" class="nav-link">作品</a>
                </li>
                <li class="nav-item">
                    <a href="#services" class="nav-link">服务</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">联系</a>
                </li>
            </ul>
            <button class="hamburger" aria-label="切换导航菜单" aria-expanded="false">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </button>
        </div>
    </nav>

    <!-- 首页部分 -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
            </div>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    <span class="title-line">创造</span>
                    <span class="title-line">非凡体验</span>
                </h1>
                <p class="hero-subtitle">专业前端设计师，致力于打造令人惊艳的数字体验</p>
                <div class="hero-buttons">
                    <a href="#portfolio" class="btn btn-primary">查看作品</a>
                    <a href="#contact" class="btn btn-secondary">联系我</a>
                </div>
            </div>
            <div class="hero-image">
                <div class="image-container">
                    <div class="image-placeholder">
                        <i class="fas fa-code"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- 关于部分 -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">关于我</h2>
                <p class="section-subtitle">专注于创造美好的数字体验</p>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <h3>设计理念</h3>
                    <p>我相信优秀的设计不仅仅是美观，更是功能性与美学的完美结合。每一个项目都是一次创新的机会，我致力于创造既实用又令人印象深刻的数字体验。</p>
                    <div class="skills">
                        <div class="skill-item">
                            <span class="skill-name">前端开发</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="95%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">UI/UX设计</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">响应式设计</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="98%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="about-stats">
                    <div class="stat-item">
                        <span class="stat-number" data-count="150">0</span>
                        <span class="stat-label">完成项目</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="98">0</span>
                        <span class="stat-label">客户满意度</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="5">0</span>
                        <span class="stat-label">年经验</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 作品集部分 -->
    <section id="portfolio" class="portfolio">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">精选作品</h2>
                <p class="section-subtitle">展示我的最佳作品和创意项目</p>
            </div>
            <div class="portfolio-filter">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="web">网站</button>
                <button class="filter-btn" data-filter="app">应用</button>
                <button class="filter-btn" data-filter="design">设计</button>
            </div>
            <div class="portfolio-grid">
                <div class="portfolio-item" data-category="web">
                    <div class="portfolio-image">
                        <div class="image-placeholder">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <h3>企业官网</h3>
                            <p>现代化企业网站设计</p>
                            <a href="#" class="portfolio-link" aria-label="查看企业官网项目详情"><i class="fas fa-external-link-alt"></i></a>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item" data-category="app">
                    <div class="portfolio-image">
                        <div class="image-placeholder">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <h3>移动应用</h3>
                            <p>用户友好的移动端体验</p>
                            <a href="#" class="portfolio-link" aria-label="查看移动应用项目详情"><i class="fas fa-external-link-alt"></i></a>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item" data-category="design">
                    <div class="portfolio-image">
                        <div class="image-placeholder">
                            <i class="fas fa-paint-brush"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <h3>品牌设计</h3>
                            <p>完整的视觉识别系统</p>
                            <a href="#" class="portfolio-link" aria-label="查看品牌设计项目详情"><i class="fas fa-external-link-alt"></i></a>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item" data-category="web">
                    <div class="portfolio-image">
                        <div class="image-placeholder">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <h3>电商平台</h3>
                            <p>高转化率的购物体验</p>
                            <a href="#" class="portfolio-link" aria-label="查看电商平台项目详情"><i class="fas fa-external-link-alt"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 服务部分 -->
    <section id="services" class="services">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">专业服务</h2>
                <p class="section-subtitle">为您提供全方位的设计解决方案</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>前端开发</h3>
                    <p>使用最新技术栈创建高性能、响应式的网站和应用程序</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>UI/UX设计</h3>
                    <p>以用户为中心的设计理念，创造直观且美观的用户界面</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>响应式设计</h3>
                    <p>确保您的网站在所有设备上都能完美显示和运行</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系部分 -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">联系我</h2>
                <p class="section-subtitle">让我们一起创造令人惊艳的项目</p>
            </div>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+86 138 0000 0000</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>中国，上海</span>
                    </div>
                </div>
                <form class="contact-form">
                    <div class="form-group">
                        <input type="text" placeholder="您的姓名" required>
                    </div>
                    <div class="form-group">
                        <input type="email" placeholder="您的邮箱" required>
                    </div>
                    <div class="form-group">
                        <textarea placeholder="项目描述" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">发送消息</button>
                </form>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 Portfolio. 保留所有权利。</p>
                <div class="social-links">
                    <a href="#" aria-label="访问GitHub主页"><i class="fab fa-github"></i></a>
                    <a href="#" aria-label="访问LinkedIn主页"><i class="fab fa-linkedin"></i></a>
                    <a href="#" aria-label="访问Twitter主页"><i class="fab fa-twitter"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
